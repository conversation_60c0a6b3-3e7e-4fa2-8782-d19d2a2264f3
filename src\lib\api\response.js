import { NextResponse } from "next/server";

export function successResponse(data, status = 200, message = "Success") {
  return NextResponse.json(
    {
      success: true,
      message,
      data,
    },
    { status }
  );
}

export function errorResponse(message, status = 500, details = null) {
  const response = {
    success: false,
    message,
    error: {
      status,
      message,
    },
  };

  if (details) {
    response.error.details = details;
  }

  return NextResponse.json(response, { status });
}

export function validationErrorResponse(errors) {
  return NextResponse.json(
    {
      success: false,
      message: "Validation failed",
      error: {
        status: 400,
        message: "Validation failed",
        details: errors,
      },
    },
    { status: 400 }
  );
}

export function handleApiError(error) {
  console.error("API Error:", error);

  if (error.message === "User not found") {
    return errorResponse("User not found", 404);
  }

  if (error.message.includes("already exists")) {
    return errorResponse(error.message, 409);
  }

  if (error.message.includes("Validation")) {
    return errorResponse(error.message, 400);
  }

  if (
    error.message.includes("database") ||
    error.message.includes("connection")
  ) {
    return errorResponse("Database connection error", 503);
  }

  return errorResponse("Internal server error", 500);
}

export function getClientIP(request) {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const remoteAddr = request.headers.get("remote-addr");

  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (remoteAddr) {
    return remoteAddr;
  }
  return "unknown";
}

export function getUserAgent(request) {
  return request.headers.get("user-agent") || "unknown";
}
