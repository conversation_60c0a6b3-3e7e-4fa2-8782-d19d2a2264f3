body {
  @apply overscroll-none bg-transparent;
}

:root {
  --font-sans: var(--font-inter);
  --header-height: calc(var(--spacing) * 12 + 1px);
}

.animate-twinkle {
  animation: twinkle 1.5s ease-in-out infinite;
}

.theme-scaled {
  @media (min-width: 1024px) {
    --radius: 0.6rem;
    --text-lg: 1.05rem;
    --text-base: 0.85rem;
    --text-sm: 0.8rem;
    --spacing: 0.222222rem;
  }

  [data-slot="card"] {
    --spacing: 0.16rem;
  }

  [data-slot="select-trigger"],
  [data-slot="toggle-group-item"] {
    --spacing: 0.222222rem;
  }
}

.theme-default,
.theme-default-scaled {
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);
  --chart-1: var(--color-neutral-600);
  --chart-2: var(--color-neutral-500);
  --chart-3: var(--color-neutral-400);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
    --chart-1: var(--color-neutral-500);
    --chart-2: var(--color-neutral-400);
    --chart-3: var(--color-neutral-300);
  }
}

.theme-blue,
.theme-blue-scaled {
  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-blue-50);
  --chart-1: var(--color-blue-600);
  --chart-2: var(--color-blue-500);
  --chart-3: var(--color-blue-400);

  @variant dark {
    --primary: var(--color-blue-500);
    --primary-foreground: var(--color-blue-50);
    --chart-1: var(--color-blue-500);
    --chart-2: var(--color-blue-400);
    --chart-3: var(--color-blue-300);
  }
}

.theme-green,
.theme-green-scaled {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);
  --chart-1: var(--color-lime-600);
  --chart-2: var(--color-lime-500);
  --chart-3: var(--color-lime-400);

  @variant dark {
    --primary: var(--color-lime-600);
    --primary-foreground: var(--color-lime-50);
    --chart-1: var(--color-lime-600);
    --chart-2: var(--color-lime-500);
    --chart-3: var(--color-lime-400);
  }
}

.theme-amber,
.theme-amber-scaled {
  --primary: var(--color-amber-600);
  --primary-foreground: var(--color-amber-50);
  --chart-1: var(--color-amber-600);
  --chart-2: var(--color-amber-500);
  --chart-3: var(--color-amber-400);

  @variant dark {
    --primary: var(--color-amber-500);
    --primary-foreground: var(--color-amber-50);
    --chart-1: var(--color-amber-500);
    --chart-2: var(--color-amber-400);
    --chart-3: var(--color-amber-300);
  }
}

.theme-purple,
.theme-purple-scaled {
  --primary: var(--color-purple-600);
  --primary-foreground: var(--color-purple-50);
  --chart-1: var(--color-purple-600);
  --chart-2: var(--color-purple-500);
  --chart-3: var(--color-purple-400);

  @variant dark {
    --primary: var(--color-purple-500);
    --primary-foreground: var(--color-purple-50);
    --chart-1: var(--color-purple-500);
    --chart-2: var(--color-purple-400);
    --chart-3: var(--color-purple-300);
  }
}

.theme-pink,
.theme-pink-scaled {
  --primary: var(--color-pink-600);
  --primary-foreground: var(--color-pink-50);
  --chart-1: var(--color-pink-600);
  --chart-2: var(--color-pink-500);
  --chart-3: var(--color-pink-400);

  @variant dark {
    --primary: var(--color-pink-500);
    --primary-foreground: var(--color-pink-50);
    --chart-1: var(--color-pink-500);
    --chart-2: var(--color-pink-400);
    --chart-3: var(--color-pink-300);
  }
}

.theme-mono,
.theme-mono-scaled {
  --font-sans: var(--font-mono);
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);
  --chart-1: var(--color-neutral-600);
  --chart-2: var(--color-neutral-500);
  --chart-3: var(--color-neutral-400);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
    --chart-1: var(--color-neutral-500);
    --chart-2: var(--color-neutral-400);
    --chart-3: var(--color-neutral-300);
  }

  .rounded-xs,
  .rounded-sm,
  .rounded-md,
  .rounded-lg,
  .rounded-xl {
    @apply !rounded-none;
    border-radius: 0;
  }

  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    @apply !shadow-none;
  }

  [data-slot="toggle-group"],
  [data-slot="toggle-group-item"] {
    @apply !rounded-none !shadow-none;
  }
}
