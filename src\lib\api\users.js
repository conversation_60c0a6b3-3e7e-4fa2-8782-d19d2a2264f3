import { prisma } from "@/lib/db";
import { Prisma } from "@prisma/client";

export async function createUser(userData) {
  try {
    const user = await prisma.user.create({
      data: {
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        onboardingCompleted: true,
        createdAt: true,
        updatedAt: true,
        lastLogin: true,
      },
    });
    return user;
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === "P2002") {
        const field = error.meta?.target?.[0];
        throw new Error(`User with this ${field} already exists`);
      }
    }
    throw error;
  }
}

export async function updateUser(userId, updateData) {
  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        onboardingCompleted: true,
        createdAt: true,
        updatedAt: true,
        lastLogin: true,
      },
    });
    return user;
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === "P2025") {
        throw new Error("User not found");
      }
      if (error.code === "P2002") {
        const field = error.meta?.target?.[0];
        throw new Error(`User with this ${field} already exists`);
      }
    }
    throw error;
  }
}

export async function deleteUser(userId) {
  try {
    const user = await prisma.user.delete({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
      },
    });
    return user;
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === "P2025") {
        throw new Error("User not found");
      }
    }
    throw error;
  }
}

export async function getUserById(userId) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        onboardingCompleted: true,
        createdAt: true,
        updatedAt: true,
        lastLogin: true,
      },
    });

    if (!user) {
      throw new Error("User not found");
    }

    return user;
  } catch (error) {
    throw error;
  }
}

export async function getUserOnboardingStatus(userId) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        onboardingCompleted: true,
        createdAt: true,
      },
    });

    if (!user) {
      throw new Error("User not found");
    }

    return {
      userId: user.id,
      onboardingCompleted: user.onboardingCompleted,
      createdAt: user.createdAt,
    };
  } catch (error) {
    throw error;
  }
}

export async function createAuditLog(
  userId,
  action,
  details = null,
  ipAddress = null,
  userAgent = null
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        action,
        details,
        ipAddress,
        userAgent,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    console.error("Failed to create audit log:", error);
  }
}
