import "./globals.css";
import { cookies } from "next/headers";
import NextTopLoader from "nextjs-toploader";
import { cn } from "@/lib/utils";
import { fontVariables } from "@/lib/font";
import { ThemeProvider } from "@/components/theme-provider";
import { ActiveThemeProvider } from "@/components/active-theme";
import { Toaster } from "sonner";
import Providers from "@/components/providers";

const META_THEME_COLORS = {
  light: "#ffffff",
  dark: "#09090b",
};

export const metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export const viewport = {
  themeColor: META_THEME_COLORS.light,
};

export default async function RootLayout({ children }) {
  const cookieStore = await cookies();
  const activeThemeValue = cookieStore.get("active_theme")?.value;
  const isScaled = activeThemeValue?.endsWith("-scaled");

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                if (
                  localStorage.theme === 'dark' ||
                  ((!('theme' in localStorage) || localStorage.theme === 'system') &&
                  window.matchMedia('(prefers-color-scheme: dark)').matches)
                ) {
                  document
                    .querySelector('meta[name="theme-color"]')
                    .setAttribute('content', '${META_THEME_COLORS.dark}');
                }
              } catch (_) {}
            `,
          }}
        />
      </head>
      <body
        className={cn(
          "bg-background overflow-hidden overscroll-none font-sans antialiased",
          activeThemeValue ? `theme-${activeThemeValue}` : "",
          isScaled ? "theme-scaled" : "",
          fontVariables
        )}
      >
        <NextTopLoader showSpinner={false} />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
          enableColorScheme
        >
          <Providers initialTheme={activeThemeValue}>
            <Toaster />
            {children}
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  );
}
